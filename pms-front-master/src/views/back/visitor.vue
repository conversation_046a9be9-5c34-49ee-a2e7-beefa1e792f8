<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { Search, Plus, Edit, Delete, View, Calendar, User, Check, Clock, Close, Loading, Phone, House, ArrowDown } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import BackHeader from '@/components/BackHeader.vue';
import { addVisitorApi, getVisitorListApi, editVisitorApi, deleteVisitorApi, getVisitorByIdApi } from '@/request/visitorApi';
import { getAllResidentListApi } from '@/request/residentsApi';
import { getAllHouseListApi } from '@/request/houseApi';

// 搜索关键词
const searchKeyword = ref('');

// 当前页码
const currentPage = ref(1);
// 每页显示条数
const pageSize = ref(10);
// 总条数
const total = ref(0);
// 筛选条件
const filterValue = ref('全部状态');
// 访问日期筛选
const visitDate = ref('');

// 加载状态
const loadingVisitors = ref(false);
const loadingResidents = ref(false);
const loadingHouses = ref(false);

// 访客统计数据
const visitorStats = reactive([
  { label: '今日访客', count: 0, icon: 'Calendar', color: 'blue' },
  { label: '待审核', count: 0, icon: 'Clock', color: 'orange' },
  { label: '已通过', count: 0, icon: 'Check', color: 'green' },
  { label: '已拒绝', count: 0, icon: 'Close', color: 'red' }
]);

// 访客列表
const visitors = reactive([]);

// 住户选项
const residentOptions = reactive([]);
// 房屋选项
const houseOptions = reactive([]);

// 对话框状态
const addDialogVisible = ref(false);
const editDialogVisible = ref(false);
const viewDialogVisible = ref(false);

// 表单引用
const addFormRef = ref(null);
const editFormRef = ref(null);

// 提交状态
const isSubmitting = ref(false);
const isEditSubmitting = ref(false);

// 新访客表单数据
const newVisitor = reactive({
  residentId: '',
  houseId: '',
  visitorName: '',
  visitorPhone: '',
  visitTime: '',
  leaveTime: '',
  reason: '',
  status: '0' // 0=待审核, 1=已通过, 2=已拒绝
});

// 编辑访客表单数据
const editVisitorData = reactive({
  visitorId: '',
  residentId: '',
  houseId: '',
  visitorName: '',
  visitorPhone: '',
  visitTime: '',
  leaveTime: '',
  reason: '',
  status: '0'
});

// 查看访客详情数据
const viewVisitorData = reactive({});

// 表单验证规则
const addFormRules = {
  residentId: [{ required: true, message: '请选择住户', trigger: 'change' }],
  houseId: [{ required: true, message: '请选择房屋', trigger: 'change' }],
  visitorName: [{ required: true, message: '请输入访客姓名', trigger: 'blur' }],
  visitorPhone: [
    { required: true, message: '请输入访客电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  visitTime: [{ required: true, message: '请选择访问时间', trigger: 'change' }],
  leaveTime: [{ required: true, message: '请选择离开时间', trigger: 'change' }],
  reason: [{ required: true, message: '请输入访问原因', trigger: 'blur' }]
};

const editFormRules = { ...addFormRules };

// 工具函数
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN');
};

const formatDateTime = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleString('zh-CN');
};

const formatDateTimeForAPI = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const getStatusLabel = (status) => {
  const statusMap = {
    '0': '待审核',
    '1': '已通过',
    '2': '已拒绝'
  };
  return statusMap[status] || '未知';
};

const getStatusType = (status) => {
  const typeMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'danger'
  };
  return typeMap[status] || 'info';
};

const getFilterStatusValue = (filterText) => {
  const filterMap = {
    '待审核': '0',
    '已通过': '1',
    '已拒绝': '2'
  };
  return filterMap[filterText];
};

// 获取住户选项
const loadResidentOptions = async () => {
  try {
    loadingResidents.value = true;
    const response = await getAllResidentListApi({});

    if (response.code === 200 && response.data) {
      residentOptions.length = 0;
      response.data.forEach(resident => {
        residentOptions.push({
          value: resident.residentId || resident.id,
          label: resident.name
        });
      });
    } else {
      ElMessage.error('获取住户列表失败');
    }
  } catch (error) {
    console.error('获取住户列表失败:', error);
    ElMessage.error('获取住户列表失败，请稍后重试');
  } finally {
    loadingResidents.value = false;
  }
};

// 获取房屋选项
const loadHouseOptions = async () => {
  try {
    loadingHouses.value = true;
    const response = await getAllHouseListApi({});

    if (response.code === 200 && response.data) {
      houseOptions.length = 0;
      response.data.forEach(house => {
        houseOptions.push({
          value: house.houseId || house.id,
          label: `${house.building} ${house.room}`
        });
      });
    } else {
      ElMessage.error('获取房屋列表失败');
    }
  } catch (error) {
    console.error('获取房屋列表失败:', error);
    ElMessage.error('获取房屋列表失败，请稍后重试');
  } finally {
    loadingHouses.value = false;
  }
};

// 获取访客列表
const loadVisitorList = async () => {
  try {
    loadingVisitors.value = true;
    const response = await getVisitorListApi({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      searchKey: searchKeyword.value || '',
      status: filterValue.value === '全部状态' ? '' : getFilterStatusValue(filterValue.value),
      visitTime: visitDate.value ? new Date(visitDate.value) : null
    });

    if (response.code === 200 && response.data) {
      visitors.length = 0;
      response.data.list?.forEach(visitor => {
        visitors.push({
          ...visitor,
          statusLabel: getStatusLabel(visitor.status),
          statusType: getStatusType(visitor.status),
          visitTimeFormatted: formatDateTime(visitor.visitTime),
          leaveTimeFormatted: formatDateTime(visitor.leaveTime)
        });
      });
      total.value = response.data.total || 0;

      // 更新统计数据
      updateVisitorStats();
    } else {
      ElMessage.error('获取访客列表失败');
    }
  } catch (error) {
    console.error('获取访客列表失败:', error);
    ElMessage.error('获取访客列表失败，请稍后重试');
  } finally {
    loadingVisitors.value = false;
  }
};

// 更新访客统计数据
const updateVisitorStats = () => {
  const today = new Date().toDateString();
  let todayCount = 0;
  let pendingCount = 0;
  let approvedCount = 0;
  let rejectedCount = 0;

  visitors.forEach(visitor => {
    const visitDate = new Date(visitor.visitTime).toDateString();
    if (visitDate === today) todayCount++;

    if (visitor.status === '0') pendingCount++;
    else if (visitor.status === '1') approvedCount++;
    else if (visitor.status === '2') rejectedCount++;
  });

  visitorStats[0].count = todayCount;
  visitorStats[1].count = pendingCount;
  visitorStats[2].count = approvedCount;
  visitorStats[3].count = rejectedCount;
};

// 添加新访客
const addNewVisitor = async () => {
  await Promise.all([loadResidentOptions(), loadHouseOptions()]);

  // 重置表单数据
  Object.assign(newVisitor, {
    residentId: '',
    houseId: '',
    visitorName: '',
    visitorPhone: '',
    visitTime: '',
    leaveTime: '',
    reason: '',
    status: '0'
  });

  addDialogVisible.value = true;

  // 重置表单验证状态
  if (addFormRef.value) {
    addFormRef.value.clearValidate();
  }
};

// 提交新访客表单
const submitNewVisitor = async () => {
  if (!addFormRef.value) return;

  await addFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isSubmitting.value = true;

        const response = await addVisitorApi({
          residentId: parseInt(newVisitor.residentId),
          houseId: parseInt(newVisitor.houseId),
          visitorName: newVisitor.visitorName,
          visitorPhone: newVisitor.visitorPhone,
          visitTime: new Date(newVisitor.visitTime),
          leaveTime: new Date(newVisitor.leaveTime),
          reason: newVisitor.reason,
          status: newVisitor.status
        });

        if (response.code === 200) {
          ElMessage.success('添加访客成功');
          addDialogVisible.value = false;
          await loadVisitorList();
        } else {
          ElMessage.error(response.msg || '添加访客失败');
        }
      } catch (error) {
        console.error('添加访客失败:', error);
        ElMessage.error('添加访客失败，请稍后重试');
      } finally {
        isSubmitting.value = false;
      }
    }
  });
};

// 取消添加
const cancelAdd = () => {
  addDialogVisible.value = false;
};

// 编辑访客
const editVisitor = async (visitor) => {
  try {
    await Promise.all([loadResidentOptions(), loadHouseOptions()]);

    const response = await getVisitorByIdApi(visitor.visitorId || visitor.id);
    if (response.code === 200 && response.data) {
      Object.assign(editVisitorData, {
        visitorId: response.data.visitorId || response.data.id,
        residentId: response.data.residentId,
        houseId: response.data.houseId,
        visitorName: response.data.visitorName,
        visitorPhone: response.data.visitorPhone,
        visitTime: response.data.visitTime,
        leaveTime: response.data.leaveTime,
        reason: response.data.reason,
        status: response.data.status
      });

      editDialogVisible.value = true;

      // 重置表单验证状态
      if (editFormRef.value) {
        editFormRef.value.clearValidate();
      }
    } else {
      ElMessage.error('获取访客信息失败');
    }
  } catch (error) {
    console.error('获取访客信息失败:', error);
    ElMessage.error('获取访客信息失败，请稍后重试');
  }
};

// 提交编辑访客表单
const submitEditVisitor = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        isEditSubmitting.value = true;

        const response = await editVisitorApi({
          visitorId: parseInt(editVisitorData.visitorId),
          residentId: parseInt(editVisitorData.residentId),
          houseId: parseInt(editVisitorData.houseId),
          visitorName: editVisitorData.visitorName,
          visitorPhone: editVisitorData.visitorPhone,
          visitTime: new Date(editVisitorData.visitTime),
          leaveTime: new Date(editVisitorData.leaveTime),
          reason: editVisitorData.reason,
          status: editVisitorData.status
        });

        if (response.code === 200) {
          ElMessage.success('编辑访客成功');
          editDialogVisible.value = false;
          await loadVisitorList();
        } else {
          ElMessage.error(response.msg || '编辑访客失败');
        }
      } catch (error) {
        console.error('编辑访客失败:', error);
        ElMessage.error('编辑访客失败，请稍后重试');
      } finally {
        isEditSubmitting.value = false;
      }
    }
  });
};

// 取消编辑
const cancelEdit = () => {
  editDialogVisible.value = false;
};

// 查看访客详情
const viewVisitorDetail = async (visitor) => {
  try {
    const response = await getVisitorByIdApi(visitor.visitorId || visitor.id);
    if (response.code === 200 && response.data) {
      Object.assign(viewVisitorData, {
        ...response.data,
        statusLabel: getStatusLabel(response.data.status),
        visitTimeFormatted: formatDateTime(response.data.visitTime),
        leaveTimeFormatted: formatDateTime(response.data.leaveTime)
      });
      viewDialogVisible.value = true;
    } else {
      ElMessage.error('获取访客详情失败');
    }
  } catch (error) {
    console.error('获取访客详情失败:', error);
    ElMessage.error('获取访客详情失败，请稍后重试');
  }
};

// 关闭查看详情对话框
const closeViewDialog = () => {
  viewDialogVisible.value = false;
};

// 删除访客
const deleteVisitor = async (visitor) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除访客 "${visitor.visitorName}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    );

    const response = await deleteVisitorApi(visitor.visitorId || visitor.id);
    if (response.code === 200) {
      ElMessage.success('删除访客成功');
      await loadVisitorList();
    } else {
      ElMessage.error(response.msg || '删除访客失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除访客失败:', error);
      ElMessage.error('删除访客失败，请稍后重试');
    }
  }
};

// 审核访客
const approveVisitor = async (visitor) => {
  try {
    const response = await editVisitorApi({
      visitorId: visitor.visitorId || visitor.id,
      residentId: visitor.residentId,
      houseId: visitor.houseId,
      visitorName: visitor.visitorName,
      visitorPhone: visitor.visitorPhone,
      visitTime: new Date(visitor.visitTime),
      leaveTime: new Date(visitor.leaveTime),
      reason: visitor.reason,
      status: '1' // 已通过
    });

    if (response.code === 200) {
      ElMessage.success('审核通过成功');
      await loadVisitorList();
    } else {
      ElMessage.error(response.msg || '审核失败');
    }
  } catch (error) {
    console.error('审核失败:', error);
    ElMessage.error('审核失败，请稍后重试');
  }
};

// 拒绝访客
const rejectVisitor = async (visitor) => {
  try {
    const response = await editVisitorApi({
      visitorId: visitor.visitorId || visitor.id,
      residentId: visitor.residentId,
      houseId: visitor.houseId,
      visitorName: visitor.visitorName,
      visitorPhone: visitor.visitorPhone,
      visitTime: new Date(visitor.visitTime),
      leaveTime: new Date(visitor.leaveTime),
      reason: visitor.reason,
      status: '2' // 已拒绝
    });

    if (response.code === 200) {
      ElMessage.success('拒绝访客成功');
      await loadVisitorList();
    } else {
      ElMessage.error(response.msg || '拒绝失败');
    }
  } catch (error) {
    console.error('拒绝失败:', error);
    ElMessage.error('拒绝失败，请稍后重试');
  }
};

// 记录到达时间
const recordArrival = async (visitor) => {
  try {
    const now = new Date();
    const response = await editVisitorApi({
      visitorId: visitor.visitorId || visitor.id,
      residentId: visitor.residentId,
      houseId: visitor.houseId,
      visitorName: visitor.visitorName,
      visitorPhone: visitor.visitorPhone,
      visitTime: now, // 更新为实际到达时间
      leaveTime: new Date(visitor.leaveTime),
      reason: visitor.reason,
      status: visitor.status
    });

    if (response.code === 200) {
      ElMessage.success('记录到达时间成功');
      await loadVisitorList();
    } else {
      ElMessage.error(response.msg || '记录失败');
    }
  } catch (error) {
    console.error('记录到达时间失败:', error);
    ElMessage.error('记录到达时间失败，请稍后重试');
  }
};

// 记录离开时间
const recordDeparture = async (visitor) => {
  try {
    const now = new Date();
    const response = await editVisitorApi({
      visitorId: visitor.visitorId || visitor.id,
      residentId: visitor.residentId,
      houseId: visitor.houseId,
      visitorName: visitor.visitorName,
      visitorPhone: visitor.visitorPhone,
      visitTime: new Date(visitor.visitTime),
      leaveTime: now, // 更新为实际离开时间
      reason: visitor.reason,
      status: visitor.status
    });

    if (response.code === 200) {
      ElMessage.success('记录离开时间成功');
      await loadVisitorList();
    } else {
      ElMessage.error(response.msg || '记录失败');
    }
  } catch (error) {
    console.error('记录离开时间失败:', error);
    ElMessage.error('记录离开时间失败，请稍后重试');
  }
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadVisitorList();
};

// 处理筛选
const handleFilter = (value) => {
  filterValue.value = value;
  currentPage.value = 1;
  loadVisitorList();
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadVisitorList();
};

// 监听搜索关键词变化
watch(searchKeyword, () => {
  if (searchKeyword.value === '') {
    handleSearch();
  }
});

// 监听日期筛选变化
watch(visitDate, () => {
  currentPage.value = 1;
  loadVisitorList();
});

// 页面挂载时加载数据
onMounted(() => {
  loadVisitorList();
});
</script>

<template>
  <BackHeader activeMenu="visitor">
    <div class="visitor-container">
      <!-- 添加访客对话框 -->
      <el-dialog
        v-model="addDialogVisible"
        title="添加访客"
        width="600px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="add-visitor-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="addFormRef"
            :model="newVisitor"
            :rules="addFormRules"
            label-width="100px"
            class="visitor-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>

              <el-form-item label="住户" prop="residentId">
                <el-select
                  v-model="newVisitor.residentId"
                  placeholder="请选择住户"
                  style="width: 100%"
                  :loading="loadingResidents"
                >
                  <el-option
                    v-for="resident in residentOptions"
                    :key="resident.value"
                    :label="resident.label"
                    :value="resident.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="房屋" prop="houseId">
                <el-select
                  v-model="newVisitor.houseId"
                  placeholder="请选择房屋"
                  style="width: 100%"
                  :loading="loadingHouses"
                >
                  <el-option
                    v-for="house in houseOptions"
                    :key="house.value"
                    :label="house.label"
                    :value="house.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="访客姓名" prop="visitorName">
                <el-input
                  v-model="newVisitor.visitorName"
                  placeholder="请输入访客姓名"
                />
              </el-form-item>

              <el-form-item label="访客电话" prop="visitorPhone">
                <el-input
                  v-model="newVisitor.visitorPhone"
                  placeholder="请输入访客电话"
                />
              </el-form-item>
            </div>

            <!-- 访问信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Calendar /></el-icon>
                访问信息
              </h3>

              <el-form-item label="访问时间" prop="visitTime">
                <el-date-picker
                  v-model="newVisitor.visitTime"
                  type="datetime"
                  placeholder="选择访问时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>

              <el-form-item label="离开时间" prop="leaveTime">
                <el-date-picker
                  v-model="newVisitor.leaveTime"
                  type="datetime"
                  placeholder="选择离开时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>

              <el-form-item label="访问原因" prop="reason">
                <el-input
                  v-model="newVisitor.reason"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入访问原因"
                />
              </el-form-item>

              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="newVisitor.status"
                  placeholder="请选择状态"
                  style="width: 100%"
                >
                  <el-option label="待审核" value="0" />
                  <el-option label="已通过" value="1" />
                  <el-option label="已拒绝" value="2" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelAdd">取消</el-button>
            <el-button
              type="primary"
              @click="submitNewVisitor"
              :loading="isSubmitting"
            >
              {{ isSubmitting ? '添加中...' : '确认添加' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 编辑访客对话框 -->
      <el-dialog
        v-model="editDialogVisible"
        title="编辑访客"
        width="600px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="edit-visitor-dialog"
      >
        <div class="dialog-content">
          <el-form
            ref="editFormRef"
            :model="editVisitorData"
            :rules="editFormRules"
            label-width="100px"
            class="visitor-form"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>

              <el-form-item label="住户" prop="residentId">
                <el-select
                  v-model="editVisitorData.residentId"
                  placeholder="请选择住户"
                  style="width: 100%"
                  :loading="loadingResidents"
                >
                  <el-option
                    v-for="resident in residentOptions"
                    :key="resident.value"
                    :label="resident.label"
                    :value="resident.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="房屋" prop="houseId">
                <el-select
                  v-model="editVisitorData.houseId"
                  placeholder="请选择房屋"
                  style="width: 100%"
                  :loading="loadingHouses"
                >
                  <el-option
                    v-for="house in houseOptions"
                    :key="house.value"
                    :label="house.label"
                    :value="house.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="访客姓名" prop="visitorName">
                <el-input
                  v-model="editVisitorData.visitorName"
                  placeholder="请输入访客姓名"
                />
              </el-form-item>

              <el-form-item label="访客电话" prop="visitorPhone">
                <el-input
                  v-model="editVisitorData.visitorPhone"
                  placeholder="请输入访客电话"
                />
              </el-form-item>
            </div>

            <!-- 访问信息 -->
            <div class="form-section">
              <h3 class="section-title">
                <el-icon><Calendar /></el-icon>
                访问信息
              </h3>

              <el-form-item label="访问时间" prop="visitTime">
                <el-date-picker
                  v-model="editVisitorData.visitTime"
                  type="datetime"
                  placeholder="选择访问时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>

              <el-form-item label="离开时间" prop="leaveTime">
                <el-date-picker
                  v-model="editVisitorData.leaveTime"
                  type="datetime"
                  placeholder="选择离开时间"
                  style="width: 100%"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>

              <el-form-item label="访问原因" prop="reason">
                <el-input
                  v-model="editVisitorData.reason"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入访问原因"
                />
              </el-form-item>

              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="editVisitorData.status"
                  placeholder="请选择状态"
                  style="width: 100%"
                >
                  <el-option label="待审核" value="0" />
                  <el-option label="已通过" value="1" />
                  <el-option label="已拒绝" value="2" />
                </el-select>
              </el-form-item>
            </div>
          </el-form>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelEdit">取消</el-button>
            <el-button
              type="primary"
              @click="submitEditVisitor"
              :loading="isEditSubmitting"
            >
              {{ isEditSubmitting ? '保存中...' : '保存修改' }}
            </el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 查看访客详情对话框 -->
      <el-dialog
        v-model="viewDialogVisible"
        title="访客详情"
        width="600px"
        :close-on-click-modal="false"
        :modal="true"
        :lock-scroll="true"
        :center="true"
        :destroy-on-close="true"
        class="view-visitor-dialog"
      >
        <div class="view-content">
          <div class="detail-section">
            <h3 class="section-title">
              <el-icon><User /></el-icon>
              基本信息
            </h3>
            <div class="detail-grid">
              <div class="detail-item">
                <span class="detail-label">访客姓名：</span>
                <span class="detail-value">{{ viewVisitorData.visitorName }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">访客电话：</span>
                <span class="detail-value">{{ viewVisitorData.visitorPhone }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">访问时间：</span>
                <span class="detail-value">{{ viewVisitorData.visitTimeFormatted }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">离开时间：</span>
                <span class="detail-value">{{ viewVisitorData.leaveTimeFormatted }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">访问原因：</span>
                <span class="detail-value">{{ viewVisitorData.reason }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">状态：</span>
                <el-tag
                  :type="viewVisitorData.statusLabel === '已通过' ? 'success' :
                      viewVisitorData.statusLabel === '待审核' ? 'warning' : 'danger'"
                  size="small"
                >
                  {{ viewVisitorData.statusLabel }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="closeViewDialog">关闭</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">访客管理</h1>
          <p class="page-subtitle">管理小区访客登记和审核</p>
        </div>
        
        <el-button type="primary" class="add-visitor-btn" @click="addNewVisitor">
          <el-icon><Plus /></el-icon>
          添加访客
        </el-button>
      </div>
      
      <!-- 访客统计卡片 -->
      <div class="visitor-stats">
        <div v-for="(stat, index) in visitorStats" :key="index" class="stat-card">
          <div class="stat-icon-container" :class="`stat-icon-${stat.color}`">
            <el-icon v-if="stat.icon === 'Calendar'"><Calendar /></el-icon>
            <el-icon v-else-if="stat.icon === 'Clock'"><Clock /></el-icon>
            <el-icon v-else-if="stat.icon === 'Check'"><Check /></el-icon>
            <el-icon v-else><Close /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-count">{{ stat.count }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
      
      <!-- 搜索和筛选 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索访客姓名、电话、目的..."
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="filter-box">
          <el-dropdown>
            <el-button class="filter-button">
              {{ filterValue }}
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleFilter('全部状态')">全部状态</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('待审核')">待审核</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('已通过')">已通过</el-dropdown-item>
                <el-dropdown-item @click="handleFilter('已拒绝')">已拒绝</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          
          <el-date-picker
            v-model="visitDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            class="date-picker"
          />
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loadingVisitors" class="loading-container">
        <el-icon class="loading-icon" :size="40">
          <Loading />
        </el-icon>
        <p class="loading-text">加载访客列表中...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="visitors.length === 0" class="empty-container">
        <el-icon class="empty-icon" :size="60">
          <User />
        </el-icon>
        <p class="empty-text">暂无访客数据</p>
        <el-button type="primary" @click="addNewVisitor">
          <el-icon><Plus /></el-icon>
          添加访客
        </el-button>
      </div>

      <!-- 访客卡片列表 -->
      <div v-else class="visitors-grid">
        <div v-for="visitor in visitors" :key="visitor.visitorId || visitor.id" class="visitor-card">
          <div class="visitor-card-header">
            <div class="visitor-id-badge">访客 #{{ visitor.visitorId || visitor.id }}</div>
            <el-tag :type="visitor.statusType" size="small">{{ visitor.statusLabel }}</el-tag>
          </div>

          <div class="visitor-card-content">
            <div class="visitor-main-info">
              <div class="visitor-avatar">{{ visitor.visitorName.charAt(0) }}</div>
              <div class="visitor-details">
                <div class="visitor-name">{{ visitor.visitorName }}</div>
                <div class="visitor-phone">{{ visitor.visitorPhone }}</div>
              </div>
            </div>
            
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">住户ID</div>
                <div class="info-value">{{ visitor.residentId }}</div>
              </div>

              <div class="info-item">
                <div class="info-label">房屋ID</div>
                <div class="info-value">{{ visitor.houseId }}</div>
              </div>

              <div class="info-item">
                <div class="info-label">访问时间</div>
                <div class="info-value">{{ visitor.visitTimeFormatted }}</div>
              </div>

              <div class="info-item">
                <div class="info-label">访问目的</div>
                <div class="info-value purpose">{{ visitor.reason }}</div>
              </div>
            </div>

            <div class="time-records">
              <div class="time-record">
                <div class="time-label">离开时间</div>
                <div class="time-value">
                  <span v-if="visitor.leaveTimeFormatted">{{ visitor.leaveTimeFormatted }}</span>
                  <span v-else class="no-data">未记录</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="visitor-card-footer">
            <el-button type="primary" size="small" text @click="viewVisitorDetail(visitor)">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>

            <el-button type="warning" size="small" text @click="editVisitor(visitor)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>

            <template v-if="visitor.status === '0'">
              <el-button type="success" size="small" text @click="approveVisitor(visitor)">
                <el-icon><Check /></el-icon>
                通过
              </el-button>
              <el-button type="danger" size="small" text @click="rejectVisitor(visitor)">
                <el-icon><Close /></el-icon>
                拒绝
              </el-button>
            </template>

            <template v-if="visitor.status === '1'">
              <el-button type="info" size="small" text @click="recordArrival(visitor)">
                记录到达
              </el-button>
              <el-button type="info" size="small" text @click="recordDeparture(visitor)">
                记录离开
              </el-button>
            </template>

            <el-button type="danger" size="small" text @click="deleteVisitor(visitor)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <div class="total-info">共 {{ total }} 条记录</div>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </BackHeader>
</template>

<style scoped>
.visitor-container {
  padding: 0 20px 20px;
  max-width: 1800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 10px 0 0 0;
}

.add-visitor-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 48px;
  font-size: 16px;
  padding: 0 20px;
}

.visitor-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  flex: 1;
  background-color: #fff;
  border-radius: 12px;
  padding: 28px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.stat-icon-container {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-icon-blue {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.stat-icon-orange {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stat-icon-green {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.stat-icon-red {
  background-color: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stat-icon-container .el-icon {
  font-size: 32px;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-count {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #909399;
}

.search-filter-bar {
  display: flex;
  margin-bottom: 32px;
}

.search-box {
  flex: 1;
  margin-right: 20px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.filter-box {
  display: flex;
  gap: 16px;
}

.filter-button {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.date-picker {
  width: 180px;
}

/* 访客卡片网格布局 */
.visitors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 1200px) {
  .visitors-grid {
    grid-template-columns: 1fr;
  }
}

.visitor-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.visitor-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.visitor-id-badge {
  background-color: #f5f7fa;
  color: #909399;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.visitor-card-content {
  padding: 20px;
  flex: 1;
}

.visitor-main-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.visitor-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  margin-right: 16px;
}

.visitor-details {
  display: flex;
  flex-direction: column;
}

.visitor-name {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.visitor-phone {
  font-size: 14px;
  color: #909399;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 13px;
  color: #909399;
  margin-bottom: 4px;
}

.info-value {
  font-size: 15px;
  color: #303133;
}

.house-info {
  font-size: 13px;
  color: #909399;
  margin-top: 2px;
}

.purpose {
  font-weight: 500;
}

.time-records {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.time-record {
  display: flex;
  flex-direction: column;
}

.time-label {
  font-size: 13px;
  color: #909399;
  margin-bottom: 4px;
}

.time-value {
  font-size: 15px;
  color: #303133;
}

.no-data {
  color: #c0c4cc;
}

.visitor-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
}

.total-info {
  font-size: 15px;
  color: #909399;
}

/* 对话框样式 */
.dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.visitor-form {
  padding: 0 10px;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 查看详情样式 */
.view-content {
  padding: 0 10px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 13px;
  color: #909399;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #303133;
}

.rent-highlight {
  color: #409EFF;
  font-weight: 600;
}

/* 加载和空状态样式 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-icon, .empty-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
}

.loading-text, .empty-text {
  font-size: 16px;
  color: #909399;
  margin: 0 0 20px 0;
}
</style>
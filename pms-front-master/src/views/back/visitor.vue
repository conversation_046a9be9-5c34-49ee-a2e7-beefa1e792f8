<script setup>
import { ref, reactive } from 'vue';
import { Search, Plus, Edit, Delete, View, Calendar, User, Check, Clock } from '@element-plus/icons-vue';
import BackHeader from '@/components/BackHeader.vue';

// 搜索关键词
const searchKeyword = ref('');

// 访客统计数据
const visitorStats = reactive([
  { label: '今日访客', count: 5, icon: 'Calendar', color: 'blue' },
  { label: '待审核', count: 3, icon: 'Clock', color: 'orange' },
  { label: '已通过', count: 12, icon: 'Check', color: 'green' },
  { label: '已拒绝', count: 2, icon: 'Close', color: 'red' }
]);

// 访客记录列表
const visitorRecords = reactive([
  {
    id: 1,
    name: '李四',
    phone: '13900139000',
    visitDate: '2024-02-15',
    visitTime: '14:00-17:00',
    purpose: '送快递',
    status: '已通过',
    statusType: 'success',
    resident: {
      name: '张三',
      house: 'A栋1201'
    },
    arrivalTime: '14:30',
    departureTime: '14:45'
  },
  {
    id: 2,
    name: '王五',
    phone: '13700137000',
    visitDate: '2024-02-20',
    visitTime: '09:00-12:00',
    purpose: '探亲访友',
    status: '待审核',
    statusType: 'warning',
    resident: {
      name: '李四',
      house: 'B栋801'
    },
    arrivalTime: '',
    departureTime: ''
  },
  {
    id: 3,
    name: '赵六',
    phone: '13800138000',
    visitDate: '2024-02-18',
    visitTime: '10:00-11:30',
    purpose: '维修水管',
    status: '已通过',
    statusType: 'success',
    resident: {
      name: '王五',
      house: 'C栋1505'
    },
    arrivalTime: '10:05',
    departureTime: '11:20'
  },
  {
    id: 4,
    name: '钱七',
    phone: '13600136000',
    visitDate: '2024-02-16',
    visitTime: '16:00-18:00',
    purpose: '送外卖',
    status: '已拒绝',
    statusType: 'danger',
    resident: {
      name: '赵六',
      house: 'A栋506'
    },
    arrivalTime: '',
    departureTime: ''
  },
  {
    id: 5,
    name: '孙八',
    phone: '13500135000',
    visitDate: '2024-02-22',
    visitTime: '13:00-15:00',
    purpose: '探亲访友',
    status: '待审核',
    statusType: 'warning',
    resident: {
      name: '钱七',
      house: 'D栋302'
    },
    arrivalTime: '',
    departureTime: ''
  }
]);

// 添加新访客
const addNewVisitor = () => {
  console.log('添加新访客');
  // 实际应用中这里应该打开添加访客的表单或对话框
};

// 处理搜索
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value);
  // 实际应用中这里应该调用API进行搜索
};

// 筛选条件
const filterValue = ref('全部状态');

// 当前页码
const currentPage = ref(1);
// 总条数
const total = ref(visitorRecords.length);

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  // 实际应用中这里应该重新加载数据
};

// 查看访客详情
const viewVisitorDetail = (visitor) => {
  console.log('查看访客详情:', visitor);
  // 实际应用中这里应该打开访客详情页面或对话框
};

// 审核访客
const approveVisitor = (visitor) => {
  console.log('审核通过访客:', visitor);
  // 实际应用中这里应该调用API进行审核
};

// 拒绝访客
const rejectVisitor = (visitor) => {
  console.log('拒绝访客:', visitor);
  // 实际应用中这里应该调用API进行拒绝
};

// 记录到达时间
const recordArrival = (visitor) => {
  console.log('记录到达时间:', visitor);
  // 实际应用中这里应该打开记录到达时间的表单或对话框
};

// 记录离开时间
const recordDeparture = (visitor) => {
  console.log('记录离开时间:', visitor);
  // 实际应用中这里应该打开记录离开时间的表单或对话框
};
</script>

<template>
  <BackHeader activeMenu="visitor">
    <div class="visitor-container">
      <!-- 页面标题和添加按钮 -->
      <div class="page-header">
        <div class="title-section">
          <h1 class="page-title">访客管理</h1>
          <p class="page-subtitle">管理小区访客登记和审核</p>
        </div>
        
        <el-button type="primary" class="add-visitor-btn" @click="addNewVisitor">
          <el-icon><Plus /></el-icon>
          添加访客
        </el-button>
      </div>
      
      <!-- 访客统计卡片 -->
      <div class="visitor-stats">
        <div v-for="(stat, index) in visitorStats" :key="index" class="stat-card">
          <div class="stat-icon-container" :class="`stat-icon-${stat.color}`">
            <el-icon v-if="stat.icon === 'Calendar'"><Calendar /></el-icon>
            <el-icon v-else-if="stat.icon === 'Clock'"><Clock /></el-icon>
            <el-icon v-else-if="stat.icon === 'Check'"><Check /></el-icon>
            <el-icon v-else><Close /></el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-count">{{ stat.count }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
      
      <!-- 搜索和筛选 -->
      <div class="search-filter-bar">
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索访客姓名、电话、目的..."
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="filter-box">
          <el-dropdown>
            <el-button class="filter-button">
              {{ filterValue }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>全部状态</el-dropdown-item>
                <el-dropdown-item>待审核</el-dropdown-item>
                <el-dropdown-item>已通过</el-dropdown-item>
                <el-dropdown-item>已拒绝</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          
          <el-date-picker
            v-model="visitDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            class="date-picker"
          />
        </div>
      </div>
      
      <!-- 访客卡片列表 -->
      <div class="visitors-grid">
        <div v-for="visitor in visitorRecords" :key="visitor.id" class="visitor-card">
          <div class="visitor-card-header">
            <div class="visitor-id-badge">访客 #{{ visitor.id }}</div>
            <el-tag :type="visitor.statusType" size="small">{{ visitor.status }}</el-tag>
          </div>
          
          <div class="visitor-card-content">
            <div class="visitor-main-info">
              <div class="visitor-avatar">{{ visitor.name.charAt(0) }}</div>
              <div class="visitor-details">
                <div class="visitor-name">{{ visitor.name }}</div>
                <div class="visitor-phone">{{ visitor.phone }}</div>
              </div>
            </div>
            
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">被访人</div>
                <div class="info-value">
                  <div>{{ visitor.resident.name }}</div>
                  <div class="house-info">{{ visitor.resident.house }}</div>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-label">访问日期</div>
                <div class="info-value">{{ visitor.visitDate }}</div>
              </div>
              
              <div class="info-item">
                <div class="info-label">访问时间</div>
                <div class="info-value">{{ visitor.visitTime }}</div>
              </div>
              
              <div class="info-item">
                <div class="info-label">访问目的</div>
                <div class="info-value purpose">{{ visitor.purpose }}</div>
              </div>
            </div>
            
            <div class="time-records">
              <div class="time-record">
                <div class="time-label">到达时间</div>
                <div class="time-value">
                  <span v-if="visitor.arrivalTime">{{ visitor.arrivalTime }}</span>
                  <span v-else class="no-data">未记录</span>
                </div>
              </div>
              
              <div class="time-record">
                <div class="time-label">离开时间</div>
                <div class="time-value">
                  <span v-if="visitor.departureTime">{{ visitor.departureTime }}</span>
                  <span v-else class="no-data">未记录</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="visitor-card-footer">
            <el-button type="primary" size="small" text @click="viewVisitorDetail(visitor)">
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            
            <template v-if="visitor.status === '待审核'">
              <el-button type="success" size="small" text @click="approveVisitor(visitor)">
                <el-icon><Check /></el-icon>
                通过
              </el-button>
              <el-button type="danger" size="small" text @click="rejectVisitor(visitor)">
                <el-icon><Close /></el-icon>
                拒绝
              </el-button>
            </template>
            
            <template v-if="visitor.status === '已通过' && !visitor.arrivalTime">
              <el-button type="warning" size="small" text @click="recordArrival(visitor)">
                记录到达
              </el-button>
            </template>
            
            <template v-if="visitor.status === '已通过' && visitor.arrivalTime && !visitor.departureTime">
              <el-button type="info" size="small" text @click="recordDeparture(visitor)">
                记录离开
              </el-button>
            </template>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <div class="total-info">共 {{ total }} 条记录</div>
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </BackHeader>
</template>

<style scoped>
.visitor-container {
  padding: 0 20px 20px;
  max-width: 1800px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 10px 0 0 0;
}

.add-visitor-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  height: 48px;
  font-size: 16px;
  padding: 0 20px;
}

.visitor-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  flex: 1;
  background-color: #fff;
  border-radius: 12px;
  padding: 28px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.stat-icon-container {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.stat-icon-blue {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.stat-icon-orange {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.stat-icon-green {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.stat-icon-red {
  background-color: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

.stat-icon-container .el-icon {
  font-size: 32px;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-count {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #909399;
}

.search-filter-bar {
  display: flex;
  margin-bottom: 32px;
}

.search-box {
  flex: 1;
  margin-right: 20px;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__inner) {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.filter-box {
  display: flex;
  gap: 16px;
}

.filter-button {
  height: 52px;
  font-size: 16px;
  padding: 0 20px;
}

.date-picker {
  width: 180px;
}

/* 访客卡片网格布局 */
.visitors-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

@media (max-width: 1200px) {
  .visitors-grid {
    grid-template-columns: 1fr;
  }
}

.visitor-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.visitor-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.visitor-id-badge {
  background-color: #f5f7fa;
  color: #909399;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.visitor-card-content {
  padding: 20px;
  flex: 1;
}

.visitor-main-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.visitor-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  margin-right: 16px;
}

.visitor-details {
  display: flex;
  flex-direction: column;
}

.visitor-name {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.visitor-phone {
  font-size: 14px;
  color: #909399;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 13px;
  color: #909399;
  margin-bottom: 4px;
}

.info-value {
  font-size: 15px;
  color: #303133;
}

.house-info {
  font-size: 13px;
  color: #909399;
  margin-top: 2px;
}

.purpose {
  font-weight: 500;
}

.time-records {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.time-record {
  display: flex;
  flex-direction: column;
}

.time-label {
  font-size: 13px;
  color: #909399;
  margin-bottom: 4px;
}

.time-value {
  font-size: 15px;
  color: #303133;
}

.no-data {
  color: #c0c4cc;
}

.visitor-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
}

.total-info {
  font-size: 15px;
  color: #909399;
}
</style>
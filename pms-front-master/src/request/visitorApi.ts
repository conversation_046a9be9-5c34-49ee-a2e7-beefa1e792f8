import request from "./request";
import { ReportData } from "@/types/api";

export const addVisitorApi = (data: {
    residentId: number;
    houseId: number;
    visitorName: string;
    visitorPhone: string;
    visitTime: string;
    leaveTime: string;
    reason: string;
    status: string;
}): Promise<ReportData<any>> => {
    return request.post('/visitor/add', data)
}

export const getVisitorListApi = (data: {
    pageNum: number;
    pageSize: number;
    searchKey: string;
    residentId: number;
    houseId: number;
    visitorName: string;
    visitorPhone: string;
    visitTime: Date;
    leaveTime: Date;
    reason: string;
    status: string;
}): Promise<ReportData<any>> => {
    return request.post('/visitor/pageList', data)
}

export const getAllVisitorListApi = (data: {
    searchKey: string;
    residentId: number;
    houseId: number;
    visitorName: string;
    visitorPhone: string;
    visitTime: Date;
    leaveTime: Date;
    reason: string;
    status: string;
}): Promise<ReportData<any>> => {
    return request.post('/visitor/list', data)
}

export const editVisitorApi = (data: {
    visitorId: number;
    residentId: number;
    houseId: number;
    visitorName: string;
    visitorPhone: string;
    visitTime: Date;
    leaveTime: Date;
    reason: string;
    status: string;
}): Promise<ReportData<any>> => {
    return request.put('/visitor/edit', data)
}

export const deleteVisitorApi = (visitorId: number): Promise<ReportData<any>> => {
    return request.delete(`/visitor/delete/${visitorId}`)
}

export const getVisitorByIdApi = (visitorId: number): Promise<ReportData<any>> => {
    return request.get(`/visitor/getById/${visitorId}`)
}